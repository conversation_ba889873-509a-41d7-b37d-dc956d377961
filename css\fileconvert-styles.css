/* File Converter Styles */
.file-title {
  background: linear-gradient(135deg, #4a90e2, #63b3ed);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
  font-size: 2.5rem;
  text-shadow: 0 2px 10px rgba(99, 179, 237, 0.2);
}

.hero-section {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.05) 0%, rgba(99, 179, 237, 0.1) 100%);
  border-radius: 15px;
  padding: 40px;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: url('../assets/pattern-dots.svg') no-repeat;
  opacity: 0.1;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.subtitle {
  font-size: 1.2rem;
  color: var(--accent-color);
  margin-bottom: 15px;
  font-weight: 500;
}

.hero-description {
  font-size: 1rem;
  color: var(--text-color);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Upload Box Styles */
.upload-box {
  background-color: var(--card-bg);
  border-radius: 15px;
  padding: 40px;
  margin-bottom: 30px;
  text-align: center;
  border: 2px dashed var(--border-color);
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.upload-box.drag-over {
  border-color: var(--highlight-color);
  background-color: var(--card-highlight);
}

.upload-icon {
  font-size: 3rem;
  color: var(--accent-color);
  margin-bottom: 20px;
}

.upload-label {
  display: inline-block;
  background-color: var(--highlight-color);
  color: black;
  padding: 12px 25px;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-bottom: 15px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.upload-label:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.upload-label i {
  margin-right: 8px;
}

input[type="file"] {
  display: none;
}

.drag-text {
  color: var(--text-color);
  margin-bottom: 20px;
  font-size: 0.95rem;
}

.upload-info {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.upload-info p {
  color: var(--accent-light);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* File Details Card */
.details-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 0;
  margin-top: 25px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: none;
  text-align: left;
  border: 1px solid var(--border-color);
}

.details-header {
  background-color: var(--secondary-color);
  padding: 12px 20px;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid var(--border-color);
}

.details-content {
  padding: 15px 20px;
}

.details-content p {
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color);
}

.details-content p i {
  color: var(--accent-color);
  width: 20px;
  text-align: center;
}

/* Conversion Options */
.conversion-options {
  background-color: var(--card-bg);
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.options-header h3 {
  font-size: 1.2rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

/* Format Selection */
.format-selection {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.format-option {
  background-color: var(--secondary-color);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.format-option:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--highlight-color);
}

.format-option.active {
  background-color: var(--card-highlight);
  border-color: var(--highlight-color);
}

.format-icon {
  font-size: 2rem;
  color: var(--accent-color);
  margin-bottom: 10px;
}

.format-option.active .format-icon {
  color: var(--highlight-color);
}

.format-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
}

/* Comment Box */
.comment-box {
  background-color: #ffebee; /* Light red background */
  border-left: 4px solid #f44336; /* Red border */
  color: #d32f2f; /* Dark red text */
  padding: 15px 20px;
  margin: 25px 0;
  border-radius: 5px;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.1);
}

.comment-box h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-box p {
  margin: 0;
  line-height: 1.5;
}

/* Action Button */
.action-button {
  background-color: var(--highlight-color);
  color: black;
  border: none;
  padding: 12px 25px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 0 auto 30px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.action-button.secondary {
  background-color: var(--secondary-color);
  color: var(--text-color);
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* How It Works Section */
.how-it-works {
  background-color: var(--card-bg);
  border-radius: 15px;
  padding: 40px;
  margin-top: 40px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.how-it-works h2 {
  text-align: center;
  margin-bottom: 30px;
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.step {
  text-align: center;
  padding: 20px;
  border-radius: 10px;
  background-color: var(--secondary-color);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.step:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.step-icon {
  position: relative;
  width: 70px;
  height: 70px;
  background-color: var(--card-highlight);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 1.5rem;
  color: var(--highlight-color);
}

.step-number {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 25px;
  height: 25px;
  background-color: var(--highlight-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 700;
  color: black;
}

.step h3 {
  margin-bottom: 10px;
  color: var(--text-color);
  font-size: 1.1rem;
}

.step p {
  color: var(--accent-light);
  font-size: 0.9rem;
  line-height: 1.5;
}

.tech-info {
  background-color: var(--secondary-color);
  border-radius: 10px;
  padding: 20px;
  border: 1px solid var(--border-color);
}

.tech-info h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.tech-info p {
  color: var(--accent-light);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .hero-section {
    padding: 30px 20px;
  }
  
  .file-title {
    font-size: 2rem;
  }
  
  .upload-box {
    padding: 30px 20px;
  }
  
  .conversion-options {
    padding: 25px 20px;
  }
  
  .format-selection {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .how-it-works {
    padding: 30px 20px;
  }
  
  .steps-container {
    grid-template-columns: 1fr;
  }
}
