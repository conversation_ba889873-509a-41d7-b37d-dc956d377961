/* PDF Compressor Specific Styles */

/* PDF Title Styling */
.pdf-title {
  display: inline-block;
  margin-left: 15px;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(to right, var(--text-color), var(--highlight-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  vertical-align: middle;
}

/* PDF Upload Box Customization */
.upload-box .upload-icon .fa-file-pdf {
  font-size: 3.5rem;
  color: var(--accent-light);
  animation: float 3s infinite ease-in-out;
}

/* PDF Preview Styling */
#pdf-preview {
  margin-top: 25px;
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

#pdf-preview .pdf-thumbnail {
  max-width: 100%;
  max-height: 250px;
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  transition: all var(--transition-speed) ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
  background-color: #fff;
}

#pdf-preview .pdf-thumbnail:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

#pdf-preview .pdf-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 0.8rem;
}

/* Quality Options Styling */
.quality-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 25px;
  justify-content: center;
}

.quality-option {
  display: flex;
  align-items: center;
  gap: 15px;
  background-color: var(--secondary-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 15px;
  width: calc(33.33% - 10px);
  min-width: 200px;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
}

.quality-option:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
  border-color: var(--accent-light);
}

.quality-option.active {
  border-color: var(--highlight-color);
  background-color: rgba(255, 255, 255, 0.05);
}

.quality-option i {
  font-size: 1.5rem;
  color: var(--accent-light);
}

.quality-option.active i {
  color: var(--highlight-color);
}

.quality-text {
  flex: 1;
}

.quality-text h4 {
  margin: 0 0 5px 0;
  font-size: 1rem;
  font-weight: 600;
}

.quality-text p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--accent-light);
}

/* Advanced Options Styling */
.advanced-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 10px;
}

.option-group {
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  padding: 15px;
  flex: 1;
  min-width: 250px;
}

.option-group h4 {
  margin: 0 0 15px 0;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-group h4 i {
  color: var(--highlight-color);
}

.option {
  margin-bottom: 12px;
}

.option label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  cursor: pointer;
}

.option select {
  background-color: var(--secondary-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
  width: 100%;
  margin-top: 5px;
  cursor: pointer;
}

.option input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--accent-color);
  cursor: pointer;
}

/* PDF Preview in Results */
.pdf-preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px dashed var(--border-color);
}

.pdf-preview-placeholder i {
  font-size: 3rem;
  color: var(--accent-light);
  margin-bottom: 10px;
}

.pdf-preview-placeholder p {
  font-size: 1rem;
  color: var(--accent-light);
  margin-bottom: 5px;
}

.pdf-preview-placeholder small {
  font-size: 0.8rem;
  color: var(--accent-light);
  opacity: 0.7;
}

/* Compression Badge */
.compression-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--highlight-color);
  color: black;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.5s ease-out;
}

.compression-badge i {
  font-size: 0.9rem;
}

/* Loading indicator */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--accent-light);
  gap: 10px;
}

.loading-indicator i {
  font-size: 2rem;
  color: var(--highlight-color);
}

/* Preview Download Button */
.preview-download-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--highlight-color);
  color: black;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  z-index: 10;
}

.preview-download-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.preview-download-btn i {
  font-size: 1.2rem;
}

/* Fallback Download Button */
.fallback-download-btn {
  margin-top: 15px;
  padding: 10px 20px;
  background-color: var(--highlight-color);
  color: black;
  border: none;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.fallback-download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* Preview Container Styling */
.preview-container {
  width: 100%;
  padding: 20px;
  background-color: var(--secondary-color);
  background-image: var(--card-gradient);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 250px;
}

/* Size Comparison Styling */
.size-comparison {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
}

.size-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.size-item i {
  font-size: 1.8rem;
  color: var(--accent-light);
}

.size-item.compressed i {
  color: var(--highlight-color);
}

.size-details {
  display: flex;
  flex-direction: column;
}

.size-label {
  font-size: 0.8rem;
  color: var(--accent-light);
  margin: 0;
}

.size-value {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.size-arrow {
  color: var(--accent-light);
  font-size: 1.2rem;
}

/* Compression Stats Styling */
.compression-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 20px;
}

.stat {
  text-align: center;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--accent-light);
  margin: 0 0 5px 0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--highlight-color);
}

/* Responsive Adjustments for PDF Compressor */
@media (max-width: 768px) {
  .pdf-title {
    display: block;
    margin-left: 0;
    margin-top: 10px;
    font-size: 2rem;
  }

  .quality-option {
    width: 100%;
  }

  .size-comparison {
    flex-direction: column;
    gap: 10px;
  }

  .size-arrow {
    transform: rotate(90deg);
  }
}

@media (max-width: 480px) {
  .pdf-title {
    font-size: 1.6rem;
  }

  .option-group {
    width: 100%;
  }

  .compression-stats {
    flex-direction: column;
    gap: 15px;
  }
}
