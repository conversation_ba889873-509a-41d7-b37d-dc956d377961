<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDF Compressor - ImgNinja</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/responsive.css">
  <link rel="stylesheet" href="css/pdfcom-styles.css">
  <!-- Add Poppins Font -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  <!-- Add Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <!-- Favicon -->
  <link rel="icon" href="assets/favicon-imgNinja.png" type="image/png">
</head>
<body>
  <!-- Navigation Bar -->
  <nav>
    <div class="logo">
      <a href="index.html">
        <img src="assets/logo-imgNinja.png" alt="ImgNinja Logo" width="170" height="90">
      </a>
    </div>
    <div class="menu-toggle" id="mobile-menu">
      <span class="bar"></span>
      <span class="bar"></span>
      <span class="bar"></span>
    </div>
    <ul class="nav-links">
      <li><a href="index.html"><i class="fas fa-home"></i> Home</a></li>
      <li><a href="index.html"><i class="fas fa-image"></i> Image Compress</a></li>
      <li><a href="file-converter.html"><i class="fas fa-file-alt"></i> File Converter</a></li>
      <li><a href="pdf-compressor.html"><i class="fas fa-file-pdf"></i> PDF Compress</a></li>
      <li><a href="pages/about-us.html"><i class="fas fa-info-circle"></i> About</a></li>
    </ul>
  </nav>

  <!-- Main Content -->
  <div class="container">
    <div class="hero-section">
      <div class="hero-content">
        <h1>
          <span class="pdf-title">PDF Compressor</span>
        </h1>
        <p class="subtitle">Reduce PDF file size without losing quality</p>
        <p class="hero-description">Advanced compression technology that preserves document quality while reducing file size by up to 90%.</p>
      </div>
    </div>
    <div class="upload-box" id="drop-area">
      <div class="upload-icon">
        <i class="fas fa-file-pdf"></i>
      </div>
      <input type="file" id="pdf-upload" accept=".pdf">
      <label for="pdf-upload" class="upload-label"><i class="fas fa-cloud-upload-alt"></i> Choose a PDF</label>
      <p class="drag-text">or drag and drop your PDF here</p>
      <div class="upload-info">
        <p class="formats"><i class="fas fa-file-pdf"></i> Supported Format: PDF</p>
        <p class="max-size"><i class="fas fa-weight-hanging"></i> Max File Size: 100MB</p>
      </div>
      <div id="pdf-preview"></div>
      <!-- PDF Details Integrated Here -->
      <div id="pdf-details" class="details-card">
        <div class="details-header">
          <i class="fas fa-info-circle"></i> PDF Details
        </div>
        <div class="details-content">
          <p id="pdf-name"><i class="fas fa-file-pdf"></i> Name: <span></span></p>
          <p id="pdf-pages"><i class="fas fa-copy"></i> Pages: <span></span></p>
          <p id="pdf-size"><i class="fas fa-database"></i> Size: <span></span></p>
        </div>
      </div>
    </div>

    <!-- Compression Settings -->
    <div class="settings-card">
      <div class="settings-header">
        <h3><i class="fas fa-sliders-h"></i> Compression Settings</h3>
        <div class="settings-tabs">
          <button class="settings-tab active tooltip" data-tab="quality">
            <i class="fas fa-compress"></i> Quality
            <span class="tooltip-text">Adjust compression quality</span>
          </button>
          <button class="settings-tab tooltip" data-tab="advanced">
            <i class="fas fa-cogs"></i> Advanced
            <span class="tooltip-text">Advanced compression options</span>
          </button>
        </div>
      </div>

      <div class="settings">
        <div class="settings-panel active" id="quality-panel">
          <div class="compression-level-container">
            <div class="setting-label">
              <label for="compression-level"><i class="fas fa-compress"></i> Compression Level:</label>
            </div>
            <div class="slider-container">
              <div class="slider-track">
                <div class="slider-fill"></div>
                <input type="range" id="compression-level" min="0" max="100" value="70">
                <div class="slider-thumb"></div>
              </div>
              <div class="compression-value-container">
                <span id="compression-value">70%</span>
              </div>
            </div>
          </div>
          <div class="quality-info">
            <div class="quality-option active" data-quality="high">
              <i class="fas fa-star"></i>
              <div class="quality-text">
                <h4>High Quality</h4>
                <p>Best for documents with images and graphics</p>
              </div>
            </div>
            <div class="quality-option" data-quality="balanced">
              <i class="fas fa-balance-scale"></i>
              <div class="quality-text">
                <h4>Balanced</h4>
                <p>Good balance between size and quality</p>
              </div>
            </div>
            <div class="quality-option" data-quality="small">
              <i class="fas fa-compress-arrows-alt"></i>
              <div class="quality-text">
                <h4>Small Size</h4>
                <p>Maximum compression, good for text documents</p>
              </div>
            </div>
          </div>
        </div>

        <div class="settings-panel" id="advanced-panel">
          <div class="advanced-options">
            <div class="option-group">
              <h4><i class="fas fa-image"></i> Image Options</h4>
              <div class="option">
                <label for="image-quality">Image Quality:</label>
                <select id="image-quality">
                  <option value="high">High (300 DPI)</option>
                  <option value="medium" selected>Medium (150 DPI)</option>
                  <option value="low">Low (72 DPI)</option>
                </select>
              </div>
              <div class="option">
                <label for="image-downscale">
                  <input type="checkbox" id="image-downscale" checked>
                  Downscale large images
                </label>
              </div>
            </div>
            <div class="option-group">
              <h4><i class="fas fa-font"></i> Font Options</h4>
              <div class="option">
                <label for="font-subset">
                  <input type="checkbox" id="font-subset" checked>
                  Subset embedded fonts
                </label>
              </div>
            </div>
            <div class="option-group">
              <h4><i class="fas fa-trash-alt"></i> Cleanup Options</h4>
              <div class="option">
                <label for="remove-metadata">
                  <input type="checkbox" id="remove-metadata" checked>
                  Remove metadata
                </label>
              </div>
              <div class="option">
                <label for="remove-annotations">
                  <input type="checkbox" id="remove-annotations">
                  Remove annotations
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <button id="compress-btn" class="action-button"><i class="fas fa-compress-arrows-alt"></i> Compress PDF</button>

    <div class="result-container">
      <div id="preview" class="preview-container">
        <div class="pdf-preview-placeholder">
          <i class="fas fa-file-pdf"></i>
          <p>PDF Preview</p>
        </div>
      </div>
      <div class="result-info">
        <div class="size-comparison">
          <div class="size-item original">
            <i class="fas fa-file-pdf"></i>
            <div class="size-details">
              <p class="size-label">Original Size</p>
              <p id="original-size-display" class="size-value">0 KB</p>
            </div>
          </div>
          <div class="size-arrow">
            <i class="fas fa-arrow-right"></i>
          </div>
          <div class="size-item compressed">
            <i class="fas fa-file-archive"></i>
            <div class="size-details">
              <p class="size-label">Compressed Size</p>
              <p id="compressed-size-display" class="size-value">0 KB</p>
            </div>
          </div>
        </div>
        <div class="compression-stats">
          <div class="stat">
            <p class="stat-label">Reduction</p>
            <p id="reduction-percentage" class="stat-value">0%</p>
          </div>
          <div class="stat">
            <p class="stat-label">Saved</p>
            <p id="saved-space" class="stat-value">0 KB</p>
          </div>
        </div>
        <div class="action-buttons">
          <button id="download-btn" class="action-button" style="display: none;"><i class="fas fa-download"></i> Download Compressed PDF</button>
          <button id="new-pdf-btn" class="action-button secondary" style="display: none;"><i class="fas fa-redo-alt"></i> New PDF</button>
        </div>
      </div>
    </div>

    <!-- How It Works Section -->
    <div class="how-it-works">
      <h2><i class="fas fa-cogs"></i> How It Works</h2>
      <div class="steps-container">
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-upload"></i>
            <div class="step-number">1</div>
          </div>
          <h3>Upload Your PDF</h3>
          <p>Select a PDF from your device or drag and drop it into the upload area.</p>
        </div>
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-sliders-h"></i>
            <div class="step-number">2</div>
          </div>
          <h3>Adjust Settings</h3>
          <p>Choose your desired compression level using the slider or select from preset quality options.</p>
        </div>
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-compress-arrows-alt"></i>
            <div class="step-number">3</div>
          </div>
          <h3>Compress</h3>
          <p>Click the "Compress PDF" button to start the compression process. Our advanced algorithms will optimize your PDF while preserving quality.</p>
        </div>
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-download"></i>
            <div class="step-number">4</div>
          </div>
          <h3>Download</h3>
          <p>Once compression is complete, download your optimized PDF. It's that simple!</p>
        </div>
      </div>
      <div class="tech-info">
        <h3><i class="fas fa-microchip"></i> Our Technology</h3>
        <p>ImgNinja uses advanced PDF compression algorithms that intelligently analyze your documents to reduce file size while maintaining visual quality. Our technology works entirely in your browser - your PDFs are never uploaded to any server, ensuring complete privacy and security.</p>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer>
    <div class="footer-content">
      <!-- Column 1: Logo and About -->
      <div class="footer-column">
        <a href="index.html" class="footer-logo">
          <img src="assets/logo-imgNinja.png" alt="ImgNinja Logo" width="200" height="110">
        </a>
        <p class="footer-description">
          ImgNinja provides powerful, free online tools for image and document compression, helping you optimize your files without sacrificing quality.
        </p>
        <div class="social-links">
          <a href="#" class="social-link" title="Facebook"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link" title="Twitter"><i class="fab fa-twitter"></i></a>
          <a href="#" class="social-link" title="Instagram"><i class="fab fa-instagram"></i></a>
          <a href="#" class="social-link" title="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
        </div>
      </div>

      <!-- Column 2: Quick Links -->
      <div class="footer-column">
        <h3 class="footer-heading">Quick Links</h3>
        <div class="footer-links">
          <a href="index.html"><i class="fas fa-home"></i> Home</a>
          <a href="blog/index.html"><i class="fas fa-blog"></i> Blog</a>
          <a href="pages/about-us.html"><i class="fas fa-info-circle"></i> About Us</a>
          <a href="pages/contact-us.html"><i class="fas fa-envelope"></i> Contact Us</a>
          <a href="#"><i class="fas fa-question-circle"></i> Help & Support</a>
        </div>
      </div>

      <!-- Column 3: Legal -->
      <div class="footer-column">
        <h3 class="footer-heading">Legal</h3>
        <div class="footer-links">
          <a href="pages/privacy-policy.html"><i class="fas fa-shield-alt"></i> Privacy Policy</a>
          <a href="pages/terms-conditions.html"><i class="fas fa-file-contract"></i> Terms & Conditions</a>
          <a href="pages/dmca.html"><i class="fas fa-copyright"></i> DMCA</a>
          <a href="#"><i class="fas fa-cookie"></i> Cookie Policy</a>
        </div>
      </div>

      <!-- Column 4: Tools -->
      <div class="footer-column">
        <h3 class="footer-heading">Our Tools</h3>
        <div class="footer-links">
          <a href="index.html"><i class="fas fa-image"></i> Image Compress</a>
          <a href="pdf-compressor.html"><i class="fas fa-file-pdf"></i> PDF Compress</a>
          <a href="file-converter.html"><i class="fas fa-file-alt"></i> File Converter</a>
          <a href="#"><i class="fas fa-crop-alt"></i> Image Resizer</a>
          <a href="#"><i class="fas fa-object-group"></i> Image Editor</a>
        </div>
      </div>

      <!-- Footer Bottom -->
      <div class="footer-bottom">
        <p class="copyright">&copy; 2025 ImgNinja. All rights reserved. | Made by <a href="https://totalsolution.42web.io/" target="_blank">Total Solution</a></p>
      </div>
    </div>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top" title="Back to Top"><i class="fas fa-arrow-up"></i></a>
  </footer>

  <!-- Back to Top Script -->
  <script>
    // Back to top button functionality
    document.addEventListener('DOMContentLoaded', function() {
      const backToTopButton = document.querySelector('.back-to-top');

      // Show/hide button based on scroll position
      window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
          backToTopButton.classList.add('visible');
        } else {
          backToTopButton.classList.remove('visible');
        }
      });

      // Smooth scroll to top when clicked
      backToTopButton.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
    });
  </script>

  <!-- PDF.js library for PDF handling -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>

  <!-- App Scripts -->
  <script src="js/script.js"></script>
  <script src="js/pdfcom-script.js"></script>
</body>
</html>
